import {
  useEffect,
  useRef,
  useImperativeHandle,
  forwardRef,
  useState,
} from "react";
import mapboxgl from "mapbox-gl";
import "mapbox-gl/dist/mapbox-gl.css";

mapboxgl.accessToken = import.meta.env.VITE_MAPBOX_TOKEN;

const MapView = forwardRef(
  (
    {
      onMapClick,
      onMouseMove,
      onPointClick,
      startPoint,
      startPointRotation,
      isRotatingStartPoint,
      rotationMode,
      endPoint,
      fairwayLine,
      walkingPath,
      completedFairways,
      completedWalkingPaths,
      currentMode,
      curveControlPoints,
      onCurvePointDrag,
      curveControlPoint,
      onMovableCurvePointDrag,
      originalStraightLine,
      onStopRotation,
      onStartClockwiseRotation,
      onStartAnticlockwiseRotation,
    },
    ref
  ) => {
    const mapContainer = useRef(null);
    const mapRef = useRef(null);
    const startMarkerRef = useRef(null);
    const endMarkerRef = useRef(null);
    const completedMarkersRef = useRef([]); // Array to store all completed fairway markers
    const curveControlMarkersRef = useRef([]); // Array to store curve control point markers
    const curveDistanceMarkersRef = useRef([]); // Array to store curve distance markers
    const movableCurveControlMarkerRef = useRef(null); // Store the movable curve control marker
    const [cursorPosition, setCursorPosition] = useState(null);
    const activePopupRef = useRef(null); // Track active popup

    // Helper function to calculate distance between two coordinates using Haversine formula
    const calculateDistance = (coord1, coord2) => {
      const [lng1, lat1] = coord1;
      const [lng2, lat2] = coord2;

      const R = 6371e3; // Earth's radius in meters
      const φ1 = (lat1 * Math.PI) / 180;
      const φ2 = (lat2 * Math.PI) / 180;
      const Δφ = ((lat2 - lat1) * Math.PI) / 180;
      const Δλ = ((lng2 - lng1) * Math.PI) / 180;

      const a =
        Math.sin(Δφ / 2) * Math.sin(Δφ / 2) +
        Math.cos(φ1) * Math.cos(φ2) * Math.sin(Δλ / 2) * Math.sin(Δλ / 2);
      const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

      return R * c; // Distance in meters
    };

    // Helper function to show popup with auto-close and single popup management
    const showPopup = (coordinates, content) => {
      // Close existing popup if any
      if (activePopupRef.current) {
        activePopupRef.current.remove();
      }

      // Create new popup
      const popup = new mapboxgl.Popup({
        closeOnClick: true,
        closeButton: true,
        className: "fairway-info-popup",
      })
        .setLngLat(coordinates)
        .setHTML(content)
        .addTo(mapRef.current);

      // Store reference to active popup
      activePopupRef.current = popup;

      // Auto-close after 3 seconds
      setTimeout(() => {
        if (activePopupRef.current === popup) {
          popup.remove();
          activePopupRef.current = null;
        }
      }, 3000);

      // Clear reference when popup is closed manually
      popup.on("close", () => {
        if (activePopupRef.current === popup) {
          activePopupRef.current = null;
        }
      });
    };

    // Expose map instance to parent component
    useImperativeHandle(ref, () => ({
      getMap: () => mapRef.current,
      fitBounds: (bounds, options) => {
        if (mapRef.current) {
          mapRef.current.fitBounds(bounds, options);
        }
      },
    }));

    useEffect(() => {
      const map = new mapboxgl.Map({
        container: mapContainer.current,
        style: "mapbox://styles/mapbox/satellite-v9",
        center: [-69.99799, 41.27133], // Sankaty Head Golf Club, Massachusetts
        zoom: 13,
      });

      mapRef.current = map;

      // Wait for map to load before adding sources and layers
      map.on("load", () => {
        // Add fairway line source and layer
        map.addSource("fairway-line", {
          type: "geojson",
          data: {
            type: "Feature",
            properties: {},
            geometry: {
              type: "LineString",
              coordinates: [],
            },
          },
        });

        map.addLayer({
          id: "fairway-line",
          type: "line",
          source: "fairway-line",
          layout: {
            "line-join": "round",
            "line-cap": "round",
          },
          paint: {
            "line-color": "#007bff", // Blue color for fairway
            "line-width": 4,
          },
        });

        // Add walking path source and layer
        map.addSource("walking-path", {
          type: "geojson",
          data: {
            type: "Feature",
            properties: {},
            geometry: {
              type: "LineString",
              coordinates: [],
            },
          },
        });

        map.addLayer({
          id: "walking-path",
          type: "line",
          source: "walking-path",
          layout: {
            "line-join": "round",
            "line-cap": "round",
          },
          paint: {
            "line-color": "#fd7e14", // Orange color for walking path
            "line-width": 3,
            "line-dasharray": [2, 2], // Dashed line
          },
        });
      });

      return () => map.remove();
    }, []);

    // Separate useEffect for click handler that updates when dependencies change
    useEffect(() => {
      if (!mapRef.current) return;

      const handleMapClick = (e) => {
        // Handle rotation stopping when in rotation mode
        if (rotationMode && onStopRotation) {
          onStopRotation();
          return;
        }

        if (
          onMapClick &&
          (currentMode === "selecting-start" ||
            currentMode === "selecting-end" ||
            currentMode === "creating-walking-path" ||
            currentMode === "editing-curve" ||
            currentMode === "editing-start" ||
            currentMode === "editing-end" ||
            currentMode === "selecting-curve-direction")
        ) {
          const { lng, lat } = e.lngLat;
          onMapClick([lng, lat]);
        }
      };

      const handleMouseMove = (e) => {
        const { lng, lat } = e.lngLat;

        if (currentMode === "editing-curve") {
          setCursorPosition([lng, lat]);
        }

        if (
          onMouseMove &&
          (currentMode === "selecting-start" || currentMode === "editing-curve")
        ) {
          onMouseMove([lng, lat]);
        }
      };

      // Remove existing handlers and add new ones
      mapRef.current.off("click", handleMapClick);
      mapRef.current.off("mousemove", handleMouseMove);
      mapRef.current.on("click", handleMapClick);
      mapRef.current.on("mousemove", handleMouseMove);

      return () => {
        if (mapRef.current) {
          mapRef.current.off("click", handleMapClick);
          mapRef.current.off("mousemove", handleMouseMove);
        }
        // Clean up curve distance markers
        curveDistanceMarkersRef.current.forEach((marker) => marker.remove());
        curveDistanceMarkersRef.current = [];

        // Clean up movable curve control marker
        if (movableCurveControlMarkerRef.current) {
          movableCurveControlMarkerRef.current.remove();
          movableCurveControlMarkerRef.current = null;
        }
      };
    }, [onMapClick, currentMode]);

    // Update curve preview during curve editing
    useEffect(() => {
      if (
        !mapRef.current ||
        currentMode !== "editing-curve" ||
        !cursorPosition ||
        !startPoint ||
        !endPoint
      )
        return;

      // Create curved line preview
      const createCurvedLine = (start, control, end) => {
        const points = [];
        const numPoints = 20;

        for (let i = 0; i <= numPoints; i++) {
          const t = i / numPoints;
          const x =
            (1 - t) * (1 - t) * start[0] +
            2 * (1 - t) * t * control[0] +
            t * t * end[0];
          const y =
            (1 - t) * (1 - t) * start[1] +
            2 * (1 - t) * t * control[1] +
            t * t * end[1];
          points.push([x, y]);
        }

        return points;
      };

      const curvedLine = createCurvedLine(startPoint, cursorPosition, endPoint);

      // Update fairway line with curve preview
      const data = {
        type: "Feature",
        properties: {},
        geometry: {
          type: "LineString",
          coordinates: curvedLine,
        },
      };

      if (mapRef.current.getSource("fairway")) {
        mapRef.current.getSource("fairway").setData(data);
      }
    }, [cursorPosition, currentMode, startPoint, endPoint]);

    // Update cursor based on current mode
    useEffect(() => {
      if (mapRef.current) {
        const canvas = mapRef.current.getCanvas();
        if (
          currentMode === "selecting-start" ||
          currentMode === "selecting-end" ||
          currentMode === "creating-walking-path" ||
          currentMode === "editing-start" ||
          currentMode === "editing-end"
        ) {
          canvas.style.cursor = "crosshair";
        } else {
          canvas.style.cursor = "";
        }
      }
    }, [currentMode]);

    // Update start point marker (rotatable rectangle)
    useEffect(() => {
      if (mapRef.current) {
        // Remove existing marker
        if (startMarkerRef.current) {
          startMarkerRef.current.remove();
        }

        if (startPoint) {
          // Create larger rotatable rectangle
          const el = document.createElement("div");
          el.style.width = "32px"; // Bigger size
          el.style.height = "20px"; // Bigger size
          el.style.backgroundColor = "#28a745"; // Green color
          el.style.border = "2px solid white";
          el.style.borderRadius = "4px";
          el.style.boxShadow = "0 3px 6px rgba(0,0,0,0.4)";
          el.style.transform = `rotate(${startPointRotation || 0}deg)`;
          el.style.transformOrigin = "center";
          el.style.cursor = "pointer";

          // Add click handler for rotation stop
          el.addEventListener("click", (e) => {
            e.stopPropagation();
            if (rotationMode && onStopRotation) {
              onStopRotation();
            } else if (onPointClick && !rotationMode) {
              onPointClick("start", startPoint);
            }
          });

          startMarkerRef.current = new mapboxgl.Marker(el)
            .setLngLat(startPoint)
            .addTo(mapRef.current);
        }
      }
    }, [startPoint, startPointRotation, rotationMode, onStopRotation]);

    // Update end point marker
    useEffect(() => {
      if (mapRef.current) {
        // Remove existing marker
        if (endMarkerRef.current) {
          endMarkerRef.current.remove();
        }

        if (endPoint) {
          // Create a custom marker element for end point (golf ball)
          const el = document.createElement("div");
          el.style.width = "16px";
          el.style.height = "16px";
          el.style.backgroundColor = "white";
          el.style.border = "2px solid #333";
          el.style.borderRadius = "50%";
          el.style.boxShadow = "0 2px 4px rgba(0,0,0,0.3)";

          // Add small dimples to make it look like a golf ball
          el.innerHTML =
            '<div style="position: absolute; top: 2px; left: 2px; width: 2px; height: 2px; background: #ccc; border-radius: 50%;"></div><div style="position: absolute; top: 6px; right: 2px; width: 2px; height: 2px; background: #ccc; border-radius: 50%;"></div><div style="position: absolute; bottom: 2px; left: 6px; width: 2px; height: 2px; background: #ccc; border-radius: 50%;"></div>';

          // Add click handler for editing end point
          el.addEventListener("click", (e) => {
            e.stopPropagation();
            if (onPointClick) {
              onPointClick("end", endPoint);
            }
          });

          endMarkerRef.current = new mapboxgl.Marker(el)
            .setLngLat(endPoint)
            .addTo(mapRef.current);
        }
      }
    }, [endPoint]);

    // Update fairway line
    useEffect(() => {
      if (mapRef.current && mapRef.current.getSource("fairway-line")) {
        let data;

        // Hide original line during curve selection and editing modes
        if (
          currentMode === "selecting-curve-direction" ||
          currentMode === "editing-curve"
        ) {
          if (currentMode === "editing-curve" && fairwayLine) {
            // Show only the curved line during editing
            data = {
              type: "Feature",
              properties: {},
              geometry: {
                type: "LineString",
                coordinates: fairwayLine,
              },
            };
          } else {
            // Hide line during curve direction selection
            data = {
              type: "Feature",
              properties: {},
              geometry: {
                type: "LineString",
                coordinates: [],
              },
            };
          }
        } else if (fairwayLine) {
          // Normal fairway line display
          data = {
            type: "Feature",
            properties: {},
            geometry: {
              type: "LineString",
              coordinates: fairwayLine,
            },
          };
        } else {
          // No fairway line
          data = {
            type: "Feature",
            properties: {},
            geometry: {
              type: "LineString",
              coordinates: [],
            },
          };
        }

        mapRef.current.getSource("fairway-line").setData(data);
      }
    }, [fairwayLine, currentMode]);

    // Update walking path
    useEffect(() => {
      if (mapRef.current && mapRef.current.getSource("walking-path")) {
        const data = walkingPath
          ? {
              type: "Feature",
              properties: {},
              geometry: {
                type: "LineString",
                coordinates: walkingPath,
              },
            }
          : {
              type: "Feature",
              properties: {},
              geometry: {
                type: "LineString",
                coordinates: [],
              },
            };

        mapRef.current.getSource("walking-path").setData(data);
      }
    }, [walkingPath]);

    // Update completed fairway markers
    useEffect(() => {
      if (!mapRef.current) return;

      // Clear existing completed markers
      completedMarkersRef.current.forEach((marker) => marker.remove());
      completedMarkersRef.current = [];

      // Add markers for all completed fairways
      completedFairways.forEach((fairway) => {
        // Start point marker (green rectangle)
        const startEl = document.createElement("div");
        startEl.style.width = "32px"; // Bigger size to match current start point
        startEl.style.height = "20px"; // Bigger size to match current start point
        startEl.style.backgroundColor = "#28a745";
        startEl.style.border = "2px solid white";
        startEl.style.borderRadius = "4px";
        startEl.style.boxShadow = "0 3px 6px rgba(0,0,0,0.4)";
        startEl.style.transform = `rotate(${
          fairway.startPointRotation || 0
        }deg)`;
        startEl.style.transformOrigin = "center";
        startEl.style.cursor = "pointer";

        // Add click handler to show fairway info popup
        startEl.addEventListener("click", (e) => {
          e.stopPropagation();

          // Create popup content
          const popupContent = `
            <div style="text-align: center; font-family: Arial, sans-serif;">
              <div style="font-weight: bold; font-size: 16px; color: #1f2937; margin-bottom: 4px;">
                Fairway ${fairway.holeNumber}
              </div>
              <div style="font-size: 14px; color: #6b7280;">
                Start Point • Tap to edit
              </div>
            </div>
          `;

          showPopup(fairway.startPoint, popupContent);
        });

        const startMarker = new mapboxgl.Marker(startEl)
          .setLngLat(fairway.startPoint)
          .addTo(mapRef.current);

        // End point marker (golf ball)
        const endEl = document.createElement("div");
        endEl.style.width = "16px";
        endEl.style.height = "16px";
        endEl.style.backgroundColor = "white";
        endEl.style.border = "2px solid #333";
        endEl.style.borderRadius = "50%";
        endEl.style.boxShadow = "0 2px 4px rgba(0,0,0,0.3)";
        endEl.style.cursor = "pointer";
        endEl.innerHTML =
          '<div style="position: absolute; top: 2px; left: 2px; width: 2px; height: 2px; background: #ccc; border-radius: 50%;"></div><div style="position: absolute; top: 6px; right: 2px; width: 2px; height: 2px; background: #ccc; border-radius: 50%;"></div><div style="position: absolute; bottom: 2px; left: 6px; width: 2px; height: 2px; background: #ccc; border-radius: 50%;"></div>';

        // Add click handler to show fairway info popup for end point
        endEl.addEventListener("click", (e) => {
          e.stopPropagation();

          // Create popup content
          const popupContent = `
            <div style="text-align: center; font-family: Arial, sans-serif;">
              <div style="font-weight: bold; font-size: 16px; color: #1f2937; margin-bottom: 4px;">
                Fairway ${fairway.holeNumber}
              </div>
              <div style="font-size: 14px; color: #6b7280;">
                End Point • Tap to edit
              </div>
            </div>
          `;

          showPopup(fairway.endPoint, popupContent);
        });

        const endMarker = new mapboxgl.Marker(endEl)
          .setLngLat(fairway.endPoint)
          .addTo(mapRef.current);

        completedMarkersRef.current.push(startMarker, endMarker);
      });
    }, [completedFairways]);

    // Update completed fairway lines
    useEffect(() => {
      if (!mapRef.current) return;

      // Add completed fairway lines as separate sources
      completedFairways.forEach((fairway, index) => {
        const sourceId = `completed-fairway-${index}`;

        if (mapRef.current.getSource(sourceId)) {
          mapRef.current.removeLayer(`${sourceId}-layer`);
          mapRef.current.removeSource(sourceId);
        }

        mapRef.current.addSource(sourceId, {
          type: "geojson",
          data: {
            type: "Feature",
            properties: {},
            geometry: {
              type: "LineString",
              coordinates: fairway.fairwayLine,
            },
          },
        });

        mapRef.current.addLayer({
          id: `${sourceId}-layer`,
          type: "line",
          source: sourceId,
          layout: {
            "line-join": "round",
            "line-cap": "round",
          },
          paint: {
            "line-color": "#007cbf",
            "line-width": 4,
          },
        });
      });
    }, [completedFairways]);

    // Update completed walking paths
    useEffect(() => {
      if (!mapRef.current) return;

      // Add completed walking paths as separate sources
      completedWalkingPaths.forEach((path, index) => {
        const sourceId = `completed-walking-path-${index}`;

        if (mapRef.current.getSource(sourceId)) {
          mapRef.current.removeLayer(`${sourceId}-layer`);
          mapRef.current.removeSource(sourceId);
        }

        mapRef.current.addSource(sourceId, {
          type: "geojson",
          data: {
            type: "Feature",
            properties: {},
            geometry: {
              type: "LineString",
              coordinates: path.walkingPath,
            },
          },
        });

        mapRef.current.addLayer({
          id: `${sourceId}-layer`,
          type: "line",
          source: sourceId,
          layout: {
            "line-join": "round",
            "line-cap": "round",
          },
          paint: {
            "line-color": "#ff6b35",
            "line-width": 3,
            "line-dasharray": [2, 2],
          },
        });
      });
    }, [completedWalkingPaths]);

    // Update curve control points
    useEffect(() => {
      if (mapRef.current) {
        // Remove existing curve control markers
        curveControlMarkersRef.current.forEach((marker) => marker.remove());
        curveControlMarkersRef.current = [];

        if (curveControlPoints && curveControlPoints.length > 0) {
          curveControlPoints.forEach((point, index) => {
            const el = document.createElement("div");

            if (point.type === "middle") {
              // Middle point - draggable with dotted border
              el.style.cssText = `
                width: 30px;
                height: 30px;
                background: rgba(255, 255, 255, 0.9);
                border: 3px dashed #2563eb;
                border-radius: 50%;
                cursor: grab;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 12px;
                font-weight: bold;
                color: #2563eb;
                box-shadow: 0 2px 8px rgba(0,0,0,0.3);
              `;
              el.textContent = point.distance;

              // Add drag functionality
              let isDragging = false;

              el.addEventListener("mousedown", (e) => {
                e.stopPropagation();
                isDragging = true;
                el.style.cursor = "grabbing";
              });

              // Add mousemove listener to map for dragging
              const handleMouseMove = (e) => {
                if (isDragging && onCurvePointDrag) {
                  const { lng, lat } = e.lngLat;
                  onCurvePointDrag(index, [lng, lat]);
                }
              };

              const handleMouseUp = () => {
                if (isDragging) {
                  isDragging = false;
                  el.style.cursor = "grab";
                }
              };

              mapRef.current.on("mousemove", handleMouseMove);
              mapRef.current.on("mouseup", handleMouseUp);
            } else {
              // Quarter points - non-draggable with solid border
              el.style.cssText = `
                width: 24px;
                height: 24px;
                background: white;
                border: 2px solid #059669;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 10px;
                font-weight: bold;
                color: #059669;
                box-shadow: 0 2px 6px rgba(0,0,0,0.2);
              `;
              el.textContent = point.distance;
            }

            const marker = new mapboxgl.Marker(el)
              .setLngLat(point.position)
              .addTo(mapRef.current);

            curveControlMarkersRef.current.push(marker);
          });
        }
      }
    }, [curveControlPoints, onCurvePointDrag]);

    // Curve distance markers - show only during curve editing
    useEffect(() => {
      // Clear existing curve distance markers and movable control marker
      curveDistanceMarkersRef.current.forEach((marker) => marker.remove());
      curveDistanceMarkersRef.current = [];

      if (movableCurveControlMarkerRef.current) {
        movableCurveControlMarkerRef.current.remove();
        movableCurveControlMarkerRef.current = null;
      }

      if (
        mapRef.current &&
        currentMode === "editing-curve" &&
        fairwayLine &&
        fairwayLine.length === 3 && // Ensure it's a 3-point curve (start, control, end)
        curveControlPoint // Only show if we have a movable control point
      ) {
        const [start, control, end] = fairwayLine;

        // Calculate distances for each segment
        const segment1Distance = calculateDistance(start, control);
        const segment2Distance = calculateDistance(control, end);

        // Calculate midpoints for each segment
        const segment1Midpoint = [
          start[0] + (control[0] - start[0]) * 0.5,
          start[1] + (control[1] - start[1]) * 0.5,
        ];
        const segment2Midpoint = [
          control[0] + (end[0] - control[0]) * 0.5,
          control[1] + (end[1] - control[1]) * 0.5,
        ];

        // Since we ensure equal distances, both segments should have the same length
        // Calculate the average distance to show on both sides
        const averageDistance = Math.round(
          (segment1Distance + segment2Distance) / 2
        );

        // Create distance markers for left and right segments
        const sideMarkers = [
          {
            position: segment1Midpoint,
            distance: averageDistance,
            type: "segment",
          },
          {
            position: segment2Midpoint,
            distance: averageDistance,
            type: "segment",
          },
        ];

        // Create side distance markers
        sideMarkers.forEach((markerData) => {
          const el = document.createElement("div");

          // Segment distance markers - smaller and green
          el.style.cssText = `
            width: 32px;
            height: 32px;
            background: #10b981;
            border: 2px solid white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 10px;
            font-weight: bold;
            color: white;
            box-shadow: 0 2px 6px rgba(0,0,0,0.2);
            cursor: default;
          `;

          el.textContent = markerData.distance;

          const marker = new mapboxgl.Marker(el)
            .setLngLat(markerData.position)
            .addTo(mapRef.current);

          curveDistanceMarkersRef.current.push(marker);
        });

        // Create movable center control point (empty circle)
        const controlEl = document.createElement("div");
        controlEl.style.cssText = `
          width: 40px;
          height: 40px;
          background: transparent;
          border: 3px solid #3b82f6;
          border-radius: 50%;
          cursor: grab;
          box-shadow: 0 3px 8px rgba(0,0,0,0.3);
        `;

        // Add drag functionality to the control point with smooth dragging
        let isDragging = false;

        const handleMouseDown = (e) => {
          e.stopPropagation();
          isDragging = true;
          controlEl.style.cursor = "grabbing";

          // Only prevent map dragging while dragging the control point
          mapRef.current.dragPan.disable();
        };

        const handleMouseMove = (e) => {
          if (isDragging && onMovableCurvePointDrag) {
            e.preventDefault();
            e.stopPropagation();
            const { lng, lat } = e.lngLat;
            onMovableCurvePointDrag([lng, lat]);
          }
        };

        const handleMouseUp = (e) => {
          if (isDragging) {
            isDragging = false;
            controlEl.style.cursor = "grab";

            // Re-enable map dragging
            mapRef.current.dragPan.enable();

            if (e) {
              e.stopPropagation();
            }
          }
        };

        // Use document-level events for smooth dragging
        const handleDocumentMouseMove = (e) => {
          if (isDragging && onMovableCurvePointDrag) {
            // Convert screen coordinates to map coordinates
            const rect = mapRef.current.getContainer().getBoundingClientRect();
            const point = mapRef.current.unproject([
              e.clientX - rect.left,
              e.clientY - rect.top,
            ]);
            onMovableCurvePointDrag([point.lng, point.lat]);
          }
        };

        const handleDocumentMouseUp = (e) => {
          if (isDragging) {
            handleMouseUp(e);
          }
        };

        // Store handlers for cleanup
        const currentHandlers = {
          handleMouseDown,
          handleMouseMove,
          handleMouseUp,
          handleDocumentMouseMove,
          handleDocumentMouseUp,
        };

        // Add event listeners for smooth dragging
        controlEl.addEventListener("mousedown", handleMouseDown);

        document.addEventListener("mousemove", handleDocumentMouseMove);
        document.addEventListener("mouseup", handleDocumentMouseUp);

        // Also keep map events as backup
        mapRef.current.on("mousemove", handleMouseMove);
        mapRef.current.on("mouseup", handleMouseUp);

        // Create and store the movable control marker
        movableCurveControlMarkerRef.current = new mapboxgl.Marker(controlEl)
          .setLngLat(curveControlPoint)
          .addTo(mapRef.current);

        // Cleanup function for event listeners
        return () => {
          if (mapRef.current && currentHandlers) {
            mapRef.current.off("mousemove", currentHandlers.handleMouseMove);
            mapRef.current.off("mouseup", currentHandlers.handleMouseUp);
          }
          if (currentHandlers) {
            document.removeEventListener(
              "mousemove",
              currentHandlers.handleDocumentMouseMove
            );
            document.removeEventListener(
              "mouseup",
              currentHandlers.handleDocumentMouseUp
            );
            controlEl.removeEventListener(
              "mousedown",
              currentHandlers.handleMouseDown
            );
          }
        };
      } else if (movableCurveControlMarkerRef.current && curveControlPoint) {
        // Update existing marker position if control point changed
        movableCurveControlMarkerRef.current.setLngLat(curveControlPoint);
      }
    }, [currentMode, fairwayLine, curveControlPoint, onMovableCurvePointDrag]);

    return (
      <div
        style={{
          width: "100%",
          height: "calc(100vh - 60px)",
          position: "relative",
        }}
      >
        <div ref={mapContainer} style={{ width: "100%", height: "100%" }} />

        {/* Rotation Control Buttons - Show when start point is placed and can be rotated */}
        {startPoint &&
          currentMode === "start-placed" &&
          isRotatingStartPoint && (
            <div
              style={{
                position: "absolute",
                top: "20px",
                right: "20px",
                display: "flex",
                flexDirection: "column",
                gap: "8px",
                zIndex: 1000,
              }}
            >
              {/* Clockwise Rotation Button */}
              <button
                onClick={onStartClockwiseRotation}
                style={{
                  width: "50px",
                  height: "50px",
                  borderRadius: "50%",
                  backgroundColor: "#10b981",
                  border: "2px solid white",
                  color: "white",
                  fontSize: "20px",
                  cursor: "pointer",
                  boxShadow: "0 4px 8px rgba(0,0,0,0.3)",
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                  transition: "all 0.2s",
                }}
                onMouseOver={(e) => {
                  e.target.style.backgroundColor = "#059669";
                  e.target.style.transform = "scale(1.1)";
                }}
                onMouseOut={(e) => {
                  e.target.style.backgroundColor = "#10b981";
                  e.target.style.transform = "scale(1)";
                }}
                title="Rotate Clockwise"
              >
                🔄
              </button>

              {/* Anticlockwise Rotation Button */}
              <button
                onClick={onStartAnticlockwiseRotation}
                style={{
                  width: "50px",
                  height: "50px",
                  borderRadius: "50%",
                  backgroundColor: "#f59e0b",
                  border: "2px solid white",
                  color: "white",
                  fontSize: "20px",
                  cursor: "pointer",
                  boxShadow: "0 4px 8px rgba(0,0,0,0.3)",
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                  transition: "all 0.2s",
                }}
                onMouseOver={(e) => {
                  e.target.style.backgroundColor = "#d97706";
                  e.target.style.transform = "scale(1.1)";
                }}
                onMouseOut={(e) => {
                  e.target.style.backgroundColor = "#f59e0b";
                  e.target.style.transform = "scale(1)";
                }}
                title="Rotate Anticlockwise"
              >
                🔄
              </button>
            </div>
          )}
      </div>
    );
  }
);

MapView.displayName = "MapView";

export default MapView;
