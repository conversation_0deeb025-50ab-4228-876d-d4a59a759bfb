// Header component for the golf course mapping application

const Header = ({ onOpenPopup, onOpenWalkingPathPopup }) => {
  const handleIconClick = (iconName) => {
    console.log(`${iconName} icon clicked`);
    // TODO: Implement icon functionality later
  };

  return (
    <header
      style={{
        backgroundColor: "#001f3f", // dark navy blue
        color: "white",
        padding: "1rem",
        width: "100%",
        display: "flex",
        justifyContent: "space-between",
        alignItems: "center",
      }}
    >
      {/* Left side - Title */}
      <h1
        style={{
          fontSize: "1.5rem",
          fontWeight: "bold",
          margin: 0,
        }}
      >
        Frisbee Golf Mapper
      </h1>

      {/* Right side - Icons */}
      <div
        style={{
          display: "flex",
          gap: "16px",
          alignItems: "center",
        }}
      >
        {/* Icon 1 - Settings/Gear */}
        <button
          onClick={() => handleIconClick("Settings")}
          style={{
            background: "none",
            border: "none",
            color: "white",
            cursor: "pointer",
            fontSize: "20px",
            padding: "8px",
            borderRadius: "4px",
            transition: "background-color 0.2s",
          }}
          onMouseOver={(e) => {
            e.target.style.backgroundColor = "rgba(255, 255, 255, 0.1)";
          }}
          onMouseOut={(e) => {
            e.target.style.backgroundColor = "transparent";
          }}
          title="Settings"
        >
          ⚙️
        </button>

        {/* Icon 2 - Saved Fairways */}
        <button
          onClick={onOpenPopup}
          style={{
            background: "none",
            border: "none",
            color: "white",
            cursor: "pointer",
            fontSize: "20px",
            padding: "8px",
            borderRadius: "4px",
            transition: "background-color 0.2s",
          }}
          onMouseOver={(e) => {
            e.target.style.backgroundColor = "rgba(255, 255, 255, 0.1)";
          }}
          onMouseOut={(e) => {
            e.target.style.backgroundColor = "transparent";
          }}
          title="Saved Fairways"
        >
          📋
        </button>

        {/* Icon 3 - Walking Paths */}
        <button
          onClick={onOpenWalkingPathPopup}
          style={{
            background: "none",
            border: "none",
            color: "white",
            cursor: "pointer",
            fontSize: "20px",
            padding: "8px",
            borderRadius: "4px",
            transition: "background-color 0.2s",
            marginRight: "8px",
          }}
          onMouseOver={(e) => {
            e.target.style.backgroundColor = "rgba(255, 255, 255, 0.1)";
          }}
          onMouseOut={(e) => {
            e.target.style.backgroundColor = "transparent";
          }}
          title="Saved Walking Paths"
        >
          🚶
        </button>
      </div>
    </header>
  );
};

export default Header;
