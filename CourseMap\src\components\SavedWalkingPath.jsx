import React from "react";

const SavedWalkingPath = ({
  isOpen,
  onClose,
  completedWalkingPaths,
  onZoomToWalkingPath,
  onDeleteWalkingPath,
}) => {
  if (!isOpen) return null;

  const formatCoordinates = (coords) => {
    if (!coords || coords.length !== 2) return "N/A";
    return `${coords[0].toFixed(6)}, ${coords[1].toFixed(6)}`;
  };

  return (
    <div
      style={{
        position: "fixed",
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: "rgba(0, 0, 0, 0.5)",
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
        zIndex: 1000,
      }}
      onClick={onClose}
    >
      <div
        style={{
          backgroundColor: "white",
          borderRadius: "12px",
          padding: "24px",
          maxWidth: "500px",
          width: "90%",
          maxHeight: "80vh",
          overflowY: "auto",
          boxShadow:
            "0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)",
        }}
        onClick={(e) => e.stopPropagation()}
      >
        {/* Header */}
        <div
          style={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            marginBottom: "20px",
            borderBottom: "2px solid #e5e7eb",
            paddingBottom: "16px",
          }}
        >
          <h2
            style={{
              margin: 0,
              fontSize: "24px",
              fontWeight: "700",
              color: "#1f2937",
            }}
          >
            Saved Walking Paths - My Course
          </h2>
          <button
            onClick={onClose}
            style={{
              background: "none",
              border: "none",
              fontSize: "24px",
              cursor: "pointer",
              color: "#6b7280",
              padding: "4px",
              borderRadius: "4px",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
            }}
            onMouseOver={(e) => {
              e.target.style.backgroundColor = "#f3f4f6";
              e.target.style.color = "#374151";
            }}
            onMouseOut={(e) => {
              e.target.style.backgroundColor = "transparent";
              e.target.style.color = "#6b7280";
            }}
          >
            ✕
          </button>
        </div>

        {/* Content */}
        <div>
          {completedWalkingPaths.length === 0 ? (
            <div
              style={{
                textAlign: "center",
                padding: "40px 20px",
                color: "#6b7280",
                fontSize: "16px",
              }}
            >
              No walking paths saved yet. Create your first walking path to see
              it here!
            </div>
          ) : (
            <div
              style={{ display: "flex", flexDirection: "column", gap: "12px" }}
            >
              {completedWalkingPaths.map((path, index) => (
                <div
                  key={index}
                  style={{
                    display: "flex",
                    justifyContent: "space-between",
                    alignItems: "flex-start",
                    padding: "16px",
                    backgroundColor: "#f9fafb",
                    borderRadius: "8px",
                    border: "1px solid #e5e7eb",
                  }}
                >
                  <div style={{ flex: 1 }}>
                    <div
                      style={{
                        fontSize: "16px",
                        fontWeight: "600",
                        color: "#1f2937",
                        marginBottom: "8px",
                      }}
                    >
                      Walking Path {path.holeNumber}
                    </div>
                    <div
                      style={{
                        fontSize: "14px",
                        color: "#6b7280",
                        marginBottom: "4px",
                      }}
                    >
                      Start: {formatCoordinates(path.walkingPath?.[0])}
                    </div>
                    <div
                      style={{
                        fontSize: "14px",
                        color: "#6b7280",
                      }}
                    >
                      End:{" "}
                      {formatCoordinates(
                        path.walkingPath?.[path.walkingPath?.length - 1]
                      )}
                    </div>
                  </div>

                  <div
                    style={{ display: "flex", gap: "8px", marginLeft: "12px" }}
                  >
                    {/* Zoom Button */}
                    <button
                      onClick={() => {
                        onZoomToWalkingPath(path);
                        onClose(); // Close popup after zooming
                      }}
                      style={{
                        backgroundColor: "#3b82f6",
                        color: "white",
                        border: "none",
                        borderRadius: "6px",
                        padding: "8px 12px",
                        fontSize: "16px",
                        cursor: "pointer",
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "center",
                        transition: "all 0.2s",
                        minWidth: "40px",
                        height: "36px",
                      }}
                      onMouseOver={(e) => {
                        e.target.style.backgroundColor = "#2563eb";
                        e.target.style.transform = "translateY(-1px)";
                      }}
                      onMouseOut={(e) => {
                        e.target.style.backgroundColor = "#3b82f6";
                        e.target.style.transform = "translateY(0)";
                      }}
                    >
                      🔍
                    </button>

                    {/* Delete Button */}
                    <button
                      onClick={() => onDeleteWalkingPath(index)}
                      style={{
                        backgroundColor: "#ef4444",
                        color: "white",
                        border: "none",
                        borderRadius: "6px",
                        padding: "8px 12px",
                        fontSize: "16px",
                        cursor: "pointer",
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "center",
                        transition: "all 0.2s",
                        minWidth: "40px",
                        height: "36px",
                      }}
                      onMouseOver={(e) => {
                        e.target.style.backgroundColor = "#dc2626";
                        e.target.style.transform = "translateY(-1px)";
                      }}
                      onMouseOut={(e) => {
                        e.target.style.backgroundColor = "#ef4444";
                        e.target.style.transform = "translateY(0)";
                      }}
                    >
                      🗑️
                    </button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default SavedWalkingPath;
