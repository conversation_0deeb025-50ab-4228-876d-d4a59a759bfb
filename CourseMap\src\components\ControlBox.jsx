// ControlBox.jsx
import React from "react";

const ControlBox = ({
  statusMessage,
  currentMode,
  startPoint,
  endPoint,
  fairwayLine,
  walkingPath,
  handleContinueFairway,
  handleStartPoint,
  handleEndPoint,
  handleSaveFairway,
  handleCreateWalkingPath,
  handleUndo,
  handleSaveCourse,
  handleAddCurve,
  fairwayCount = 0,
  isFairwaySaved = false, // New prop to track if current fairway is saved
  walkingPathStartPoint = null, // New prop for walking path start point
  handleSaveWalkingPath = null, // New prop for saving walking path
  handleSaveCurve = null, // New prop for saving curve
  handleCancelCurve = null, // New prop for cancelling curve
}) => {
  // Determine which buttons should be visible based on current mode
  const shouldShowAllButtons =
    currentMode === "idle" ||
    currentMode === "selecting-end" ||
    currentMode === "fairway-saved";
  const shouldShowOnlyContinue = currentMode === "start-placed";
  const shouldShowOnlySaveFairway = currentMode === "fairway-complete";
  const shouldShowOnlySaveWalkingPath = currentMode === "creating-walking-path";
  const shouldShowCurveButtons = currentMode === "editing-curve";

  // Professional button styles
  const buttonBaseStyle = {
    border: "none",
    borderRadius: "6px",
    padding: "2px 30px",
    cursor: "pointer",
    fontSize: "14px",
    fontWeight: "500",
    fontFamily: "'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif",
    transition: "all 0.2s ease-in-out",
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    gap: "4px",
    minHeight: "40px",
    boxShadow: "0 2px 4px rgba(0,0,0,0.1)",
  };

  const primaryButtonStyle = {
    ...buttonBaseStyle,
    backgroundColor: "#2563eb",
    color: "white",
  };

  const successButtonStyle = {
    ...buttonBaseStyle,
    backgroundColor: "#059669",
    color: "white",
  };

  const warningButtonStyle = {
    ...buttonBaseStyle,
    backgroundColor: "#d97706",
    color: "white",
  };

  const disabledButtonStyle = {
    ...buttonBaseStyle,
    backgroundColor: "#e5e7eb",
    color: "#9ca3af",
    cursor: "not-allowed",
    boxShadow: "none",
  };

  const activeButtonStyle = {
    ...buttonBaseStyle,
    backgroundColor: "#1d4ed8",
    color: "white",
    boxShadow: "0 0 0 2px rgba(59, 130, 246, 0.5)",
  };

  return (
    <div
      style={{
        position: "absolute",
        bottom: "20px",
        left: "50%",
        transform: "translateX(-50%)",
        backgroundColor: "white",
        borderRadius: "12px",
        padding: "16px",
        boxShadow: "0 8px 32px rgba(0,0,0,0.12), 0 2px 8px rgba(0,0,0,0.08)",
        width: "650px",
        zIndex: 1000,
        border: "1px solid rgba(0,0,0,0.06)",
      }}
    >
      {/* Status Message */}
      <div
        style={{
          marginBottom: "6px",
          marginTop: "-6px",
          fontSize: "14px",
          color: "#374151",
          textAlign: "center",
          minHeight: "36px",
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          fontFamily:
            "'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif",
          fontWeight: "500",
          lineHeight: "1.4",
        }}
      >
        {statusMessage}
      </div>

      {/* Continue Fairway Button - Show when start point is placed */}
      {startPoint && !endPoint && currentMode === "start-placed" && (
        <div
          style={{
            marginBottom: "16px",
            display: "flex",
            justifyContent: "center",
          }}
        >
          <button
            onClick={handleContinueFairway}
            style={{
              ...primaryButtonStyle,
              backgroundColor: "#2563eb",
              fontSize: "15px",
              padding: "12px 24px",
            }}
            onMouseOver={(e) => {
              e.target.style.backgroundColor = "#1d4ed8";
              e.target.style.transform = "translateY(-1px)";
            }}
            onMouseOut={(e) => {
              e.target.style.backgroundColor = "#2563eb";
              e.target.style.transform = "translateY(0)";
            }}
          >
            <span>▶</span> Continue Fairway
          </button>
        </div>
      )}

      {/* Save Fairway Only - Show when fairway is complete but not saved */}
      {shouldShowOnlySaveFairway && (
        <div
          style={{
            marginBottom: "16px",
            display: "flex",
            justifyContent: "center",
          }}
        >
          <button
            onClick={handleSaveFairway}
            style={{
              ...successButtonStyle,
              fontSize: "15px",
              padding: "12px 24px",
            }}
            onMouseOver={(e) => {
              e.target.style.backgroundColor = "#047857";
              e.target.style.transform = "translateY(-1px)";
            }}
            onMouseOut={(e) => {
              e.target.style.backgroundColor = "#059669";
              e.target.style.transform = "translateY(0)";
            }}
          >
            <span style={{ fontSize: "16px" }}>💾</span>
            Save Fairway
          </button>
        </div>
      )}

      {/* Save Walking Path Only - Show when creating walking path */}
      {shouldShowOnlySaveWalkingPath && (
        <div
          style={{
            marginBottom: "16px",
            display: "flex",
            justifyContent: "center",
          }}
        >
          <button
            onClick={handleSaveWalkingPath}
            disabled={!walkingPath}
            style={
              walkingPath
                ? {
                    ...successButtonStyle,
                    fontSize: "15px",
                    padding: "12px 24px",
                  }
                : {
                    ...disabledButtonStyle,
                    fontSize: "15px",
                    padding: "12px 24px",
                  }
            }
            onMouseOver={(e) => {
              if (walkingPath) {
                e.target.style.backgroundColor = "#047857";
                e.target.style.transform = "translateY(-1px)";
              }
            }}
            onMouseOut={(e) => {
              if (walkingPath) {
                e.target.style.backgroundColor = "#059669";
                e.target.style.transform = "translateY(0)";
              }
            }}
          >
            <span style={{ fontSize: "16px" }}>💾</span>
            Save Walking Path
          </button>
        </div>
      )}

      {/* Curve Editing Buttons - Show when editing curve */}
      {shouldShowCurveButtons && (
        <div
          style={{
            marginBottom: "16px",
            display: "flex",
            gap: "12px",
            justifyContent: "center",
          }}
        >
          <button
            onClick={handleSaveCurve}
            style={{
              ...successButtonStyle,
              fontSize: "15px",
              padding: "12px 24px",
            }}
            onMouseOver={(e) => {
              e.target.style.backgroundColor = "#047857";
              e.target.style.transform = "translateY(-1px)";
            }}
            onMouseOut={(e) => {
              e.target.style.backgroundColor = "#059669";
              e.target.style.transform = "translateY(0)";
            }}
          >
            <span style={{ fontSize: "16px" }}>💾</span>
            Save Curve
          </button>
          <button
            onClick={handleCancelCurve}
            style={{
              ...buttonBaseStyle,
              backgroundColor: "#dc2626",
              color: "white",
              fontSize: "15px",
              padding: "12px 24px",
            }}
            onMouseOver={(e) => {
              e.target.style.backgroundColor = "#b91c1c";
              e.target.style.transform = "translateY(-1px)";
            }}
            onMouseOut={(e) => {
              e.target.style.backgroundColor = "#dc2626";
              e.target.style.transform = "translateY(0)";
            }}
          >
            <span style={{ fontSize: "16px" }}>❌</span>
            Cancel
          </button>
        </div>
      )}

      {/* Button Grid - Show all buttons except when start point is placed */}
      {shouldShowAllButtons && (
        <>
          <div
            style={{
              display: "grid",
              gridTemplateColumns: "1fr 1fr 1fr", // 3 columns for first row
              gap: "10px",
              marginBottom: "10px",
            }}
          >
            {/* First Row: Start Point, End Point, Save Fairway */}
            {/* Start Point Button */}
            <button
              onClick={handleStartPoint}
              disabled={startPoint && currentMode !== "selecting-start"}
              style={
                currentMode === "selecting-start"
                  ? activeButtonStyle
                  : startPoint
                  ? disabledButtonStyle
                  : primaryButtonStyle
              }
              onMouseOver={(e) => {
                if (!startPoint && currentMode !== "selecting-start") {
                  e.target.style.backgroundColor = "#1d4ed8";
                  e.target.style.transform = "translateY(-1px)";
                }
              }}
              onMouseOut={(e) => {
                if (!startPoint && currentMode !== "selecting-start") {
                  e.target.style.backgroundColor = "#2563eb";
                  e.target.style.transform = "translateY(0)";
                }
              }}
            >
              <span style={{ fontSize: "16px" }}>📍</span>
              {startPoint
                ? "Start Point Created"
                : currentMode === "selecting-start"
                ? "Selecting Start..."
                : "Start Point"}
            </button>

            {/* End Point Button */}
            <button
              onClick={handleEndPoint}
              disabled={
                !startPoint || endPoint || currentMode === "selecting-end"
              }
              style={
                currentMode === "selecting-end"
                  ? activeButtonStyle
                  : !startPoint || endPoint
                  ? disabledButtonStyle
                  : primaryButtonStyle
              }
              onMouseOver={(e) => {
                if (
                  startPoint &&
                  !endPoint &&
                  currentMode !== "selecting-end"
                ) {
                  e.target.style.backgroundColor = "#1d4ed8";
                  e.target.style.transform = "translateY(-1px)";
                }
              }}
              onMouseOut={(e) => {
                if (
                  startPoint &&
                  !endPoint &&
                  currentMode !== "selecting-end"
                ) {
                  e.target.style.backgroundColor = "#2563eb";
                  e.target.style.transform = "translateY(0)";
                }
              }}
            >
              <span style={{ fontSize: "16px" }}>🎯</span>
              {endPoint ? "End Point ✓" : "End Point"}
            </button>

            {/* Save Fairway Button */}
            <button
              onClick={handleSaveFairway}
              disabled={!fairwayLine}
              style={fairwayLine ? successButtonStyle : disabledButtonStyle}
              onMouseOver={(e) => {
                if (fairwayLine) {
                  e.target.style.backgroundColor = "#047857";
                  e.target.style.transform = "translateY(-1px)";
                }
              }}
              onMouseOut={(e) => {
                if (fairwayLine) {
                  e.target.style.backgroundColor = "#059669";
                  e.target.style.transform = "translateY(0)";
                }
              }}
            >
              <span style={{ fontSize: "16px" }}>💾</span>
              Save Fairway
            </button>
          </div>

          {/* Second Row: Add Curve, Create Walking Path/Save Walking Path, Undo */}
          <div
            style={{
              display: "grid",
              gridTemplateColumns: "1fr 1fr 1fr", // 3 columns for second row
              gap: "10px",
            }}
          >
            {/* Add Curve Button */}
            <button
              onClick={handleAddCurve}
              disabled={!startPoint || !endPoint || !fairwayLine}
              style={
                startPoint && endPoint && fairwayLine
                  ? {
                      ...buttonBaseStyle,
                      backgroundColor: "#8b5cf6",
                      color: "white",
                      border: "2px solid #8b5cf6",
                    }
                  : disabledButtonStyle
              }
              onMouseOver={(e) => {
                if (startPoint && endPoint && fairwayLine) {
                  e.target.style.backgroundColor = "#7c3aed";
                  e.target.style.borderColor = "#7c3aed";
                  e.target.style.transform = "translateY(-1px)";
                }
              }}
              onMouseOut={(e) => {
                if (startPoint && endPoint && fairwayLine) {
                  e.target.style.backgroundColor = "#8b5cf6";
                  e.target.style.borderColor = "#8b5cf6";
                  e.target.style.transform = "translateY(0)";
                }
              }}
            >
              <span style={{ fontSize: "16px" }}>↗️</span>
              Add Curve
            </button>

            {/* Walking Path Button - Changes based on mode */}
            {currentMode === "creating-walking-path" ? (
              // Save Walking Path Button - when walking path endpoint is placed
              <button
                onClick={handleSaveWalkingPath}
                disabled={!walkingPathStartPoint}
                style={
                  walkingPathStartPoint
                    ? successButtonStyle
                    : disabledButtonStyle
                }
                onMouseOver={(e) => {
                  if (walkingPathStartPoint) {
                    e.target.style.backgroundColor = "#047857";
                    e.target.style.transform = "translateY(-1px)";
                  }
                }}
                onMouseOut={(e) => {
                  if (walkingPathStartPoint) {
                    e.target.style.backgroundColor = "#059669";
                    e.target.style.transform = "translateY(0)";
                  }
                }}
              >
                <span style={{ fontSize: "16px" }}>💾</span>
                Save Walking Path
              </button>
            ) : (
              // Create Walking Path Button - Only enabled after fairway is saved
              <button
                disabled={currentMode !== "fairway-saved"}
                onClick={handleCreateWalkingPath}
                style={
                  currentMode === "fairway-saved"
                    ? {
                        ...buttonBaseStyle,
                        backgroundColor: "#ea580c",
                        color: "white",
                        cursor: "pointer",
                      }
                    : disabledButtonStyle
                }
                onMouseOver={(e) => {
                  if (currentMode === "fairway-saved") {
                    e.target.style.backgroundColor = "#c2410c";
                    e.target.style.transform = "translateY(-1px)";
                  }
                }}
                onMouseOut={(e) => {
                  if (currentMode === "fairway-saved") {
                    e.target.style.backgroundColor = "#ea580c";
                    e.target.style.transform = "translateY(0)";
                  }
                }}
              >
                <span style={{ fontSize: "16px" }}>🚶</span>
                Walking Path
              </button>
            )}

            {/* Undo Button */}
            <button
              onClick={handleUndo}
              style={{
                ...buttonBaseStyle,
                backgroundColor: "#dc2626",
                color: "white",
              }}
              onMouseOver={(e) => {
                e.target.style.backgroundColor = "#b91c1c";
                e.target.style.transform = "translateY(-1px)";
              }}
              onMouseOut={(e) => {
                e.target.style.backgroundColor = "#dc2626";
                e.target.style.transform = "translateY(0)";
              }}
            >
              <span style={{ fontSize: "16px" }}>↶</span>
              Undo
            </button>
          </div>
        </>
      )}

      {/* Fairway Counter */}
      <div
        style={{
          marginTop: "16px",
          textAlign: "center",
          fontSize: "14px",
          fontWeight: "600",
          color: "#374151",
        }}
      >
        Fairways Saved: {fairwayCount}
      </div>
    </div>
  );
};

export default ControlBox;
