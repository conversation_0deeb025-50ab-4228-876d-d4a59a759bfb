// Local Storage utilities for managing course data

const STORAGE_KEYS = {
  FAIRWAYS: 'golf_course_fairways',
  WALKING_PATHS: 'golf_course_walking_paths',
  COURSE_INFO: 'golf_course_info'
}

// Fairway management
export const saveFairway = (fairway) => {
  try {
    const existingFairways = getFairways()
    const fairwayIndex = existingFairways.findIndex(f => f.holeNumber === fairway.holeNumber)
    
    if (fairwayIndex >= 0) {
      // Update existing fairway
      existingFairways[fairwayIndex] = fairway
    } else {
      // Add new fairway
      existingFairways.push(fairway)
    }
    
    localStorage.setItem(STORAGE_KEYS.FAIRWAYS, JSON.stringify(existingFairways))
    return true
  } catch (error) {
    console.error('Error saving fairway:', error)
    return false
  }
}

export const getFairways = () => {
  try {
    const fairways = localStorage.getItem(STORAGE_KEYS.FAIRWAYS)
    return fairways ? JSON.parse(fairways) : []
  } catch (error) {
    console.error('Error loading fairways:', error)
    return []
  }
}

export const deleteFairway = (holeNumber) => {
  try {
    const fairways = getFairways()
    const updatedFairways = fairways.filter(f => f.holeNumber !== holeNumber)
    localStorage.setItem(STORAGE_KEYS.FAIRWAYS, JSON.stringify(updatedFairways))
    return true
  } catch (error) {
    console.error('Error deleting fairway:', error)
    return false
  }
}

// Walking path management
export const saveWalkingPath = (walkingPath) => {
  try {
    const existingPaths = getWalkingPaths()
    const pathIndex = existingPaths.findIndex(p => 
      p.fromHole === walkingPath.fromHole && p.toHole === walkingPath.toHole
    )
    
    if (pathIndex >= 0) {
      // Update existing path
      existingPaths[pathIndex] = walkingPath
    } else {
      // Add new path
      existingPaths.push(walkingPath)
    }
    
    localStorage.setItem(STORAGE_KEYS.WALKING_PATHS, JSON.stringify(existingPaths))
    return true
  } catch (error) {
    console.error('Error saving walking path:', error)
    return false
  }
}

export const getWalkingPaths = () => {
  try {
    const paths = localStorage.getItem(STORAGE_KEYS.WALKING_PATHS)
    return paths ? JSON.parse(paths) : []
  } catch (error) {
    console.error('Error loading walking paths:', error)
    return []
  }
}

export const deleteWalkingPath = (fromHole, toHole) => {
  try {
    const paths = getWalkingPaths()
    const updatedPaths = paths.filter(p => !(p.fromHole === fromHole && p.toHole === toHole))
    localStorage.setItem(STORAGE_KEYS.WALKING_PATHS, JSON.stringify(updatedPaths))
    return true
  } catch (error) {
    console.error('Error deleting walking path:', error)
    return false
  }
}

// Course info management
export const saveCourseInfo = (courseInfo) => {
  try {
    localStorage.setItem(STORAGE_KEYS.COURSE_INFO, JSON.stringify(courseInfo))
    return true
  } catch (error) {
    console.error('Error saving course info:', error)
    return false
  }
}

export const getCourseInfo = () => {
  try {
    const courseInfo = localStorage.getItem(STORAGE_KEYS.COURSE_INFO)
    return courseInfo ? JSON.parse(courseInfo) : null
  } catch (error) {
    console.error('Error loading course info:', error)
    return null
  }
}

// Clear all data
export const clearAllData = () => {
  try {
    localStorage.removeItem(STORAGE_KEYS.FAIRWAYS)
    localStorage.removeItem(STORAGE_KEYS.WALKING_PATHS)
    localStorage.removeItem(STORAGE_KEYS.COURSE_INFO)
    return true
  } catch (error) {
    console.error('Error clearing data:', error)
    return false
  }
}

// Get all data for Supabase upload
export const getAllData = () => {
  return {
    courseInfo: getCourseInfo(),
    fairways: getFairways(),
    walkingPaths: getWalkingPaths()
  }
}
