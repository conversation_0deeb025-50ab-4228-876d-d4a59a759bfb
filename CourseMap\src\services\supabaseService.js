import { supabase } from "../lib/supabase";
import { getAllData, clearAllData } from "../utils/localStorage";

// Calculate distance between two coordinates using Haversine formula
const calculateDistance = (coord1, coord2) => {
  const [lng1, lat1] = coord1;
  const [lng2, lat2] = coord2;

  const R = 6371e3; // Earth's radius in meters
  const φ1 = (lat1 * Math.PI) / 180;
  const φ2 = (lat2 * Math.PI) / 180;
  const Δφ = ((lat2 - lat1) * Math.PI) / 180;
  const Δλ = ((lng2 - lng1) * Math.PI) / 180;

  const a =
    Math.sin(Δφ / 2) * Math.sin(Δφ / 2) +
    Math.cos(φ1) * Math.cos(φ2) * Math.sin(Δλ / 2) * Math.sin(Δλ / 2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

  return R * c; // Distance in meters
};

// Save course data to Supabase
export const saveCourseToSupabase = async () => {
  try {
    const { courseInfo, fairways, walkingPaths } = getAllData();

    if (fairways.length === 0) {
      throw new Error("At least one fairway is required");
    }

    // Generate unique course ID
    const courseId = `course_${Date.now()}_${Math.random()
      .toString(36)
      .substring(2, 11)}`;

    // Prepare course data
    const courseData = {
      id: courseId,
      name: courseInfo?.name || `Course ${new Date().toLocaleDateString()}`,
      description:
        courseInfo?.description || "Golf course created with mapping app",
      created_at: new Date().toISOString(),
      total_holes: fairways.length,
      fairways: fairways.map((fairway) => ({
        hole_number: fairway.holeNumber,
        tee_location: fairway.teeLocation,
        basket_location: fairway.basketLocation,
        fairway_path: fairway.fairwayPath,
        length:
          fairway.length ||
          (fairway.teeLocation && fairway.basketLocation
            ? calculateDistance(
                fairway.teeLocation.coordinates,
                fairway.basketLocation.coordinates
              )
            : 0),
      })),
      walking_paths: walkingPaths.map((path) => ({
        from_hole: path.fromHole,
        to_hole: path.toHole,
        path_coordinates: path.pathCoordinates,
        length:
          path.length ||
          (path.pathCoordinates && path.pathCoordinates.length >= 2
            ? calculateDistance(
                path.pathCoordinates[0],
                path.pathCoordinates[path.pathCoordinates.length - 1]
              )
            : 0),
      })),
    };

    // Insert into Supabase
    const { data, error } = await supabase
      .from("courses")
      .insert([courseData])
      .select();

    if (error) {
      throw error;
    }

    // Clear local storage after successful upload
    clearAllData();

    return {
      success: true,
      data: data[0],
      message: "Course saved successfully to Supabase!",
    };
  } catch (error) {
    console.error("Error saving course to Supabase:", error);
    return {
      success: false,
      error: error.message,
      message: `Failed to save course: ${error.message}`,
    };
  }
};

// Get all courses from Supabase
export const getCoursesFromSupabase = async () => {
  try {
    const { data, error } = await supabase
      .from("courses")
      .select("*")
      .order("created_at", { ascending: false });

    if (error) {
      throw error;
    }

    return {
      success: true,
      data: data || [],
      message: "Courses loaded successfully",
    };
  } catch (error) {
    console.error("Error loading courses from Supabase:", error);
    return {
      success: false,
      error: error.message,
      message: `Failed to load courses: ${error.message}`,
    };
  }
};

// Delete a course from Supabase
export const deleteCourseFromSupabase = async (courseId) => {
  try {
    const { error } = await supabase
      .from("courses")
      .delete()
      .eq("id", courseId);

    if (error) {
      throw error;
    }

    return {
      success: true,
      message: "Course deleted successfully",
    };
  } catch (error) {
    console.error("Error deleting course from Supabase:", error);
    return {
      success: false,
      error: error.message,
      message: `Failed to delete course: ${error.message}`,
    };
  }
};
